# compile_libs.py
import os
import subprocess
import sys

# --- 配置区 ---
# 在这里列出您想要预编译的大型库
# 格式为: "库名"
LIBRARIES_TO_COMPILE = [
    # "pandas",
    # "numpy",  # pandas 依赖 numpy，最好也一起编译
    "httpx",  # 您可以添加任何其他大型或复杂的库
    # "torch",
    # "tensorflow",
]

# 预编译库的输出目录
OUTPUT_DIR = "precompiled_libs"

# Nuitka 的额外通用选项
NUITKA_COMMON_OPTIONS = [
    "--quiet",  # 减少不必要的输出，只显示警告和错误
]


# --- 配置区结束 ---


def compile_library(lib_name: str, output_dir: str):
    """使用Nuitka将单个库编译为 .nuitka-package"""
    print(f"[*] 开始编译库: {lib_name}...")

    # 首先检查库是否已安装
    try:
        import importlib.util
        spec = importlib.util.find_spec(lib_name)
        if spec is None:
            print(f"[!] 库 {lib_name} 未安装，跳过编译")
            return

        lib_path = spec.origin
        if lib_path:
            lib_dir = os.path.dirname(lib_path)
            print(f"    找到库路径: {lib_dir}")
    except Exception as e:
        print(f"[!] 检查库 {lib_name} 时出错: {e}")
        return

    # 为每个库构建特定的编译命令
    command = [
        sys.executable,  # 使用当前Python解释器
        "-m", "nuitka",
        "--module",
        lib_name,
        f"--include-package={lib_name}",
        f"--include-package-data={lib_name}",
        f"--output-dir={output_dir}",
        "--follow-imports",  # 添加这个选项来跟踪导入
    ]
    command.extend(NUITKA_COMMON_OPTIONS)

    print(f"    执行命令: {' '.join(command)}")

    try:
        # 执行编译命令
        result = subprocess.run(command, check=True, capture_output=True, text=True, encoding='utf-8')
        print(f"[+] 成功编译库: {lib_name}")
        if result.stdout:
            print("    Nuitka输出:\n", result.stdout)

    except subprocess.CalledProcessError as e:
        print(f"[!] 编译库 {lib_name} 失败!")
        print(f"    返回码: {e.returncode}")
        print("    --- STDOUT ---")
        print(e.stdout)
        print("    --- STDERR ---")
        print(e.stderr)
        # 不要立即退出，继续尝试其他库
        print(f"[*] 跳过库 {lib_name}，继续编译其他库...")


if __name__ == "__main__":
    print("--- 开始预编译大型依赖库 ---")

    # 创建输出目录（如果不存在）
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        print(f"创建目录: {OUTPUT_DIR}")

    for lib in LIBRARIES_TO_COMPILE:
        compile_library(lib, OUTPUT_DIR)

    print("\n--- 所有库预编译完成 ---")
    print(f"编译好的 .nuitka-package 文件已存放在 '{OUTPUT_DIR}' 目录下。")
    print("现在您可以运行 'compile_main_app.py' 来快速编译您的主程序了。")