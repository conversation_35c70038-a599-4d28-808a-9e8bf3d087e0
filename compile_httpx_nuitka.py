#!/usr/bin/env python3
"""
Nuitka httpx 中间包编译工具
专门用于将 httpx 及其相关依赖编译为 Nuitka 中间包
"""

import os
import sys
import json
import shutil
import subprocess
import argparse
from pathlib import Path
from typing import List, Dict, Optional


class NuitkaHttpxCompiler:
    """Nuitka httpx 包编译器"""
    
    def __init__(self, output_dir: str = "precompiled_libs"):
        self.output_dir = Path(output_dir)
        self.python_exe = sys.executable
        
        # httpx 相关包配置
        self.httpx_packages = {
            "httpx": {
                "include_data": True,
                "description": "现代异步HTTP客户端库"
            },
            "httpcore": {
                "include_data": True,
                "description": "httpx的核心HTTP传输层"
            },
            "h11": {
                "include_data": True,
                "description": "纯Python HTTP/1.1协议实现"
            },
            "h2": {
                "include_data": True,
                "description": "纯Python HTTP/2协议实现"
            },
            "hpack": {
                "include_data": True,
                "description": "HTTP/2头部压缩算法实现"
            },
            "hyperframe": {
                "include_data": True,
                "description": "HTTP/2帧层实现"
            },
            "certifi": {
                "include_data": True,
                "description": "Mozilla CA证书包"
            },
            "sniffio": {
                "include_data": True,
                "description": "异步库检测工具"
            },
            "anyio": {
                "include_data": True,
                "description": "异步I/O抽象层"
            },
            "socksio": {
                "include_data": True,
                "description": "SOCKS代理支持库"
            },
            "charset_normalizer": {
                "include_data": True,
                "description": "字符编码检测库"
            },
            "idna": {
                "include_data": True,
                "description": "国际化域名支持"
            },
            "rfc3986": {
                "include_data": True,
                "description": "URI解析库"
            }
        }
    
    def load_config(self, config_file: str) -> Dict:
        """加载配置文件"""
        config_path = Path(config_file)
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"✅ 已加载配置文件: {config_file}")
                
                # 适配现有配置文件格式
                if "dependencies" in config:
                    # 转换为内部格式
                    packages = {}
                    required_deps = config["dependencies"].get("required", [])
                    for i, pkg in enumerate(required_deps):
                        packages[pkg] = {
                            "include_data": True,
                            "description": f"Required dependency: {pkg}",
                            "priority": i + 1
                        }
                    # 更新输出目录
                    if "output_settings" in config and "output_dir" in config["output_settings"]:
                        self.output_dir = Path(config["output_settings"]["output_dir"])
                    
                    return {"packages": packages}
                
                return config
            except Exception as e:
                print(f"⚠️  配置文件加载失败: {e}")
                return {}
        else:
            print(f"⚠️  配置文件不存在: {config_file}")
            return {}
    
    def check_package_installed(self, package_name: str) -> bool:
        """检查包是否已安装"""
        try:
            __import__(package_name.replace('-', '_'))
            return True
        except ImportError:
            return False
    
    def check_dependencies(self) -> bool:
        """检查依赖"""
        print("🔍 检查编译依赖...")
        
        # 检查 Nuitka
        try:
            import nuitka
            print(f"✅ Nuitka 版本: {nuitka.__version__}")
        except ImportError:
            print("❌ 错误: 未找到 Nuitka，请安装:")
            print("   pip install nuitka")
            return False
        
        # 检查要编译的包
        missing_packages = []
        for package_name in self.httpx_packages:
            if self.check_package_installed(package_name):
                print(f"✅ {package_name} 已安装")
            else:
                missing_packages.append(package_name)
                print(f"❌ {package_name} 未安装")
        
        if missing_packages:
            print(f"\n❌ 缺少以下包: {', '.join(missing_packages)}")
            print("请先安装缺少的包:")
            print(f"   pip install {' '.join(missing_packages)}")
            return False
        
        return True
    
    def clean_output_dir(self):
        """清理输出目录"""
        if self.output_dir.exists():
            print(f"🧹 清理输出目录: {self.output_dir}")
            shutil.rmtree(self.output_dir, ignore_errors=True)
        
        self.output_dir.mkdir(parents=True, exist_ok=True)
        print(f"📁 创建输出目录: {self.output_dir}")
    
    def compile_package(self, package_name: str, package_config: Dict) -> bool:
        """编译单个包"""
        print(f"\n🔨 编译包: {package_name}")
        print(f"   描述: {package_config.get('description', 'N/A')}")
        
        # 构建 Nuitka 命令 - 修复参数格式
        cmd = [
            self.python_exe, "-m", "nuitka",
            f"--module={package_name}",  # 使用等号格式
            f"--output-dir={self.output_dir}",
            "--assume-yes-for-downloads",
            "--show-progress",
            "--show-memory"
        ]
        
        # 如果需要包含包数据
        if package_config.get("include_data", False):
            cmd.append(f"--include-package-data={package_name}")
        
        print(f"   命令: {' '.join(cmd)}")
        
        try:
            # 执行编译
            result = subprocess.run(
                cmd, 
                check=True, 
                text=True
            )
            
            print(f"✅ {package_name} 编译成功")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ {package_name} 编译失败:")
            print(f"   错误代码: {e.returncode}")
            return False
        except Exception as e:
            print(f"❌ {package_name} 编译异常: {e}")
            return False
    
    def compile_all_packages(self, packages: Optional[List[str]] = None) -> Dict[str, bool]:
        """编译所有包"""
        if packages is None:
            packages = list(self.httpx_packages.keys())
        
        print(f"\n🚀 开始编译 {len(packages)} 个包...")
        print("=" * 60)
        
        results = {}
        success_count = 0
        
        for package_name in packages:
            if package_name in self.httpx_packages:
                package_config = self.httpx_packages[package_name]
                success = self.compile_package(package_name, package_config)
                results[package_name] = success
                if success:
                    success_count += 1
            else:
                print(f"⚠️  跳过未知包: {package_name}")
                results[package_name] = False
        
        # 显示编译结果
        print("\n" + "=" * 60)
        print(f"📊 编译结果: {success_count}/{len(packages)} 成功")
        
        for package_name, success in results.items():
            status = "✅" if success else "❌"
            print(f"   {status} {package_name}")
        
        return results
    
    def list_compiled_packages(self):
        """列出已编译的包"""
        if not self.output_dir.exists():
            print(f"📁 输出目录不存在: {self.output_dir}")
            return
        
        print(f"📁 已编译的包 (位于 {self.output_dir}):")
        
        compiled_packages = []
        for item in self.output_dir.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                # 检查是否包含编译后的文件
                pyd_files = list(item.glob("*.pyd"))
                so_files = list(item.glob("*.so"))
                
                if pyd_files or so_files:
                    size = sum(f.stat().st_size for f in pyd_files + so_files)
                    size_mb = size / (1024 * 1024)
                    compiled_packages.append((item.name, size_mb))
        
        if compiled_packages:
            for name, size in sorted(compiled_packages):
                print(f"   📦 {name} ({size:.1f} MB)")
        else:
            print("   (无已编译的包)")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Nuitka httpx 包编译工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python compile_httpx_nuitka.py                    # 编译所有 httpx 相关包
  python compile_httpx_nuitka.py --packages httpx httpcore  # 编译指定包
  python compile_httpx_nuitka.py --list             # 列出已编译的包
  python compile_httpx_nuitka.py --clean            # 清理输出目录
        """
    )
    
    parser.add_argument(
        "--output-dir", "-o",
        default="precompiled_libs",
        help="输出目录 (默认: precompiled_libs)"
    )
    
    parser.add_argument(
        "--packages", "-p",
        nargs="+",
        help="指定要编译的包名"
    )
    
    parser.add_argument(
        "--config", "-c",
        default="nuitka_httpx_config.json",
        help="配置文件路径 (默认: nuitka_httpx_config.json)"
    )
    
    parser.add_argument(
        "--list", "-l",
        action="store_true",
        help="列出已编译的包"
    )
    
    parser.add_argument(
        "--clean",
        action="store_true",
        help="清理输出目录"
    )
    
    args = parser.parse_args()
    
    # 创建编译器实例
    compiler = NuitkaHttpxCompiler(args.output_dir)
    
    print("🚀 Nuitka httpx 包编译工具")
    print("=" * 60)
    
    # 处理不同的操作
    if args.list:
        compiler.list_compiled_packages()
        return
    
    if args.clean:
        compiler.clean_output_dir()
        print("✅ 清理完成")
        return
    
    # 加载配置
    config = compiler.load_config(args.config)
    if config and "packages" in config:
        compiler.httpx_packages.update(config["packages"])
    
    # 检查依赖
    if not compiler.check_dependencies():
        sys.exit(1)
    
    # 清理输出目录
    compiler.clean_output_dir()
    
    # 开始编译
    packages_to_compile = args.packages
    results = compiler.compile_all_packages(packages_to_compile)
    
    # 检查结果
    failed_packages = [name for name, success in results.items() if not success]
    if failed_packages:
        print(f"\n❌ 以下包编译失败: {', '.join(failed_packages)}")
        sys.exit(1)
    else:
        print(f"\n🎉 所有包编译成功!")
        print(f"📁 编译结果位于: {compiler.output_dir.absolute()}")


if __name__ == "__main__":
    main()
