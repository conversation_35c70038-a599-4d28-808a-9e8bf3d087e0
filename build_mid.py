import os
import sys
import subprocess
import site

# 要编译的第三方库
PACKAGE_NAME = "httpx"

# Nuitka 中间包输出目录（可以放在项目的某个缓存路径）
OUTPUT_DIR = os.path.abspath("./nuitka_cache")

# 查找库的安装位置
def find_package_path(package_name):
    for path in site.getsitepackages() + [site.getusersitepackages()]:
        pkg_path = os.path.join(path, package_name)
        if os.path.exists(pkg_path):
            return pkg_path
    raise FileNotFoundError(f"找不到 {package_name} 安装路径")

def build_nuitka_intermediate(package_name):
    pkg_path = find_package_path(package_name)
    print(f"[INFO] {package_name} 路径: {pkg_path}")

    cmd = [
        sys.executable,
        "-m", "nuitka",
        f"--include-package={PACKAGE_NAME}",  # 一次性打包整个包
        "--module",                # 编译为模块
        pkg_path,                  # 包路径
        "--output-dir=%s" % OUTPUT_DIR,
        "--nofollow-imports",      # 不跟随外部导入
        "--remove-output",         # 每次重新编译
        "--no-pyi-file",           # 不生成 .pyi
        "--lto=yes",               # 启用链接时优化
        "--jobs=4"                 # 多核编译
    ]

    print("[INFO] 编译命令:", " ".join(cmd))
    subprocess.check_call(cmd)

if __name__ == "__main__":
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    build_nuitka_intermediate(PACKAGE_NAME)
    print(f"[OK] {PACKAGE_NAME} 已编译到 {OUTPUT_DIR}")
