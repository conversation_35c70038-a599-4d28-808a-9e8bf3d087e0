{"translate_language": {"onlycnanden": "ChatTTS仅支持中文和英文配音", "peiyindayu31": "配音出错数量大于1/3，请检查", "chaochu255": "视频路径加名称过长，请缩短视频名称并移动到简短路径下，避免后续出错", "teshufuhao": "视频路径或名称中请勿存在 + & ? : | 等特殊符号，以避免后续出错", "notjson": "返回响应不是有效json数据", "fanyicuowu2": "翻译出错数量超过一半，请检查", "azureinfo": "必须正确填写 Azure TTS 的相关信息", "yuchulichucuo": "预处理阶段出错", "shibiechucuo": "语音识别阶段出错", "fanyichucuo": "翻译字幕阶段出错", "peiyinchucuo": "配音阶段出错", "hebingchucuo": "合并阶段出错", "freechatgpt_tips": "apiskey.top赞助的ChatGPT，无需sk免费使用", "yidaorujigewenjian": "已导入字幕文件:", "dakaizimubaocunmulu": "打开翻译结果保存目录", "quanbuwanbi": "全部翻译完毕", "srtgeshierror": "格式化字幕时出错，请检查字幕文件是否符合srt格式", "endopendir": "完成后点击打开保存目录", "xinshoumoshitips": "选择要翻译的视频，并设置视频原始语言和想翻译到的语言, 更完整功能更准确翻译, 请使用 标准功能模式，若翻译出错请切换到标准功能模式，使用其他翻译渠道 ", "enmodelerror": "只有当原始语言是英语时才可使用后缀为.en的模型", "openaimodelerror": "使用openai模型时, distil-whisper 开头的模型不可使用，请使用其他模型或切换到 faster模型", "qiegeshujuhaoshi": "若视频很大，此步会比较耗时，请耐心等待", "youtubehasdown": "已开始下载，请在控制台界面中查看具体进度，WARNING:信息无需理会\n点击保存目录按钮后的文本框可打开下载文件保存目录", "starting...": "处理中，请耐心等待..", "dubbing speed up": "配音加速 ", "video speed down": "视频慢放 ", "auto_ajust": "语音前后延展", "auto_ajust_tooltips": "选中此项后，若配音大于原时长，则先向后占据紧随的静音片段\n若仍不足以完全放置配音，再向前占据静音片段\n再根据是否选择了配音自动加速或视频自动慢速调整", "tts tooltip": "选择配音渠道，clone-voice/GPT-SoVITS 必须先在 左上角菜单-设置 里填写API相关信息", "trans tooltip": "选择要使用的翻译渠道，Google/Gemini/chatGPT官方接口国内无法连接，必须在右侧填写网络代理地址", "fenlinoviceerror": "分离novoice.mp4失败", "No subtitles file": "没有字幕文件", "Subtitles error": "格式化字幕信息时出错", "get video_info error": "获取视频信息时出错，请检查视频能否播放", "nogptsovitsurl": "必须填写GPT-SoVITS的api地址", "nogptsovitslanguage": "GPT-SoVITS仅支持中英日语言", "miandailigoogle": "免代理使用Google翻译", "ttsapi_nourl": "必须填写自定义TTS-API的url后才可使用", "import src error": "导入字幕出错，请检查文件内是否存在srt字幕格式内容", "openaimodelnot": "你选择的是openai模型{name}，但该{name}模型不存在，请点击菜单栏-帮助支持-下载识别模型,下载后将pt文件放在models文件夹内", "recogn result is empty": "没有识别出字幕，请检查是否包含人类说话声音，以及说话语言是否和你选择的原始语言{lang}匹配，如果都正常，请在标准功能模式下选择 '保留背景音' 重试", "pianduan": "片段", "Download Models": "下载识别模型", "Start Separate": "开始分离", "Start Separate...": "分离中/点击停止...", "Separate End/Restart": "分离完成/再次开始", "must select audio or video file": "必须选择音视频文件", "zimusrterror": "字幕区已有字幕不符合srt格式要求，请删除所有字幕区文字或重新导入正确格式的srt字幕", "Export srt": "导出字幕", "When subtitles exist, the subtitle content can be saved to a local SRT file": "当存在字幕时，可将字幕内容保存到本地srt文件", "You must fill in the YouTube video playback page address": "必须填写Youtube视频播放页地址", "Error merging background and dubbing": "背景音和配音合并时出错", "only10": "本软件在Windows平台下仅支持win10及以上系统", "sp.exeerror": "sp.exe必须位于解压后的原始目录下(即和videotrans|models|_internal在同一目录下)，请勿随意复制移动到其他位置", "meitiaozimugeshi": "每条字幕格式如下：\n\n行号数字\n开始小时:分钟:秒,毫秒 --> 结束小时:分钟:秒,毫秒\n字幕内容\n\n例如：\n\n1\n00:01:10,000 --> 00:01:20,150\n我是字幕文字哦，上面一行是本条字幕开始时间到结束时间，再上一行是行号数字1", "Set up separate dubbing roles for each subtitle to be used": "为每条字幕单独设置要使用的配音角色", "daoruzimutips": "可以从本地导入已存在的srt字幕文件，将跳过识别和翻译阶段，直接使用导入的字幕", "Click to pause and modify subtitles for more accurate processing": "点击将暂停，可以去修改字幕，以便处理更准确", "Click to start the next step immediately": "点击立即开始执行下一步操作", "zimubianjitishi": "出现暂停按钮并点击暂停后,可在此修改字幕,以便处理更准确", "test google": "正在测试连接..", "Select Out Dir": "选择保存目录", "downing...": "下载中...", "start download": "立即下载", "Down done succeed": "下载完成", "videodown..": "视频降速处理中", "Dubbing..": "配音中", "Separating background music": "正在分离背景音乐", "Unable to connect to the API": "无法连接到该API，请检查网络", "bixutianxiecloneapi": "TTS类型如何选择 clone-voice ,就必须先部署clone-voice, 并在 菜单栏-设置-原音色克隆api 中填写API地址", "Save": "保存", "Open Documents": "使用教程和帮助文档列表", "Preserve the original sound in the video": "保留视频中原声音", "Clone voice cannot be used in subtitle dubbing mode as there are no replicable voices": "字幕配音模式下不可使用clone-voice,因为不存在可复制的音色", "lanjie": "已限制", "The ott project at": "OTT离线文字翻译地址: github.com/jianchang512/ott", "No select videos": "尚未选择视频", "You must deploy and start the clone-voice service": "必须部署并启动github.com/jianchang512/clone-voice服务，将地址填写在菜单栏-设置-原音色克隆", "cannot connection to clone-voice service": "无法连接到clone-voice服务，你必须部署并启动github.com/jianchang512/clone-voice服务，将地址填写在菜单栏-设置-cloneVoice中", "test clone voice": "正在测试能否连接到clone-voice服务", "The project at": "clone-voice声音克隆地址: github.com/jianchang512/clone-voice", "selectmp4": "选择视频", "bukebaoliubeijing": "必须选择配音角色和输入视频，才能保留背景声音", "Separating vocals and background music, which may take a longer time": "正在分离人声和背景音乐，可能耗时较久", "selectsrt": "选择字幕", "selectsavedir": "选择翻译后存储目录", "proxyerrortitle": "网络代理错误", "proxyerrorbody": "无法访问google服务，请正确设置网络代理", "softname": "pyVideoTrans视频翻译配音", "anerror": "出错了", "selectvideodir": "必须选择视频", "sourenotequaltarget": "源语言和目标语言不得相同", "shoundselecttargetlanguage": "必须选择一个目标语言", "Check the progress": "在我的创作页面查看进度", "running": "执行中..", "ing": "执行中..", "exit": "退出", "end": "已结束(点击重新开始)", "stop": "已停止(点击重新开始)", "error": "出错停止(点击重新开始)", "subtitleandvoice_role": "你没有选择视频，只输入了字幕，将仅仅创建配音wav文件，请确认继续？", "waitrole": "正在获取可用配音角色，请稍等重新选择", "downloadmodel": "你当前使用的是faster模型{name}，但该{name}模型不存在，请点击菜单栏-帮助支持-下载识别模型", "modelpathis": "当前模型路径是:", "modellost": "模型下载出错或者下载不完整，请重新下载后存放到 models 目录下", "embedsubtitle": "硬字幕嵌入", "embedsubtitle2": "硬字幕嵌入(双)", "softsubtitle": "软字幕", "softsubtitle2": "软字幕(双)", "nosubtitle": "不添加字幕", "baikeymust": "你必须在菜单-设置中填写百度key", "chatgptkeymust": "你必须在菜单-设置中填写chatGPT key", "waitsubtitle": "等待编辑字幕(点击继续合成)", "waitforend": "正在合成视频", "createdirerror": "创建目录失败", "processingstatusbar": "正在处理视频:[{var1}],还有[{var2}]个在等待处理", "confirmstop": "确认停止执行?如果字幕生成一半，再次启动可能不完整，可以手动删掉tmp目录", "deepl_authkey": "你必须在菜单-设置中填写DeepL的授权key", "deepl_nosupport": "当前翻译通道不支持翻译为该语言", "wait_edit_subtitle": "等待修改字幕", "continue_action": "继续下一步", "autocomposing": "秒倒计时后将自动合成视频，你可以手动点击暂停按钮", "queding": "确定", "hechengchucuo": "合成出错，缺少文件:", "installffmpeg": "没有 ffmpeg , 到ffmpeg.org下载解压后将ffmpeg和ffprobe放到本软件根下", "yinsekelong": "音色克隆将接入 github.com/jianchang512/clone-voice ，实现自定义音色配音", "yinsekaifazhong": "音色克隆开发中", "whisper_type_all": "整体识别", "whisper_type_split": "预先分割", "whisper_type_avg": "均等分割", "fenge_tips": "整体识别:由模型自动对整个音频断句处理,多大的视频请勿选择整体识别，避免显存不足闪退\n预先分割:适合很大的视频，事先切成1分钟的小片段逐次识别和断句\n均等分割:按照固定秒数均等切割，每条字幕时长相等，时长由set.ini中interval_split控制", "waitclear": "正在关闭后台进程，请点击确定退出软件", "subtitle_tips": " 在此可编辑字幕信息，或拖动已有srt文件到此处松开 ", "setdeepl_authkey": "必须在菜单-设置中填写DeepL 授权token", "setdeeplx_address": "必须在菜单-设置中填写你的 DeepLX 地址和端口，比如 http://127.0.0.1:1188", "setott_address": "必须在菜单-设置中填写你的 OTT 地址和端口, 比如 http://127.0.0.1:9911", "mustberole": "必须选择一个配音角色", "qingqueren": "请确认", "only_srt": "没有选择目标语言，不会进行翻译和配音，仅从视频中提取字幕srt文件到目录文件夹，点Yes将继续执行，否则取消", "tencent_key": "必须在菜单-设置中填写腾讯 SecretId 和 SecretKey", "xuanzejuese": "必须选择配音角色，请先根据字幕语言选择目标语言，然后选择配音角色", "daoruzimu": "导入字幕", "shitingpeiyin": "试听配音", "xianqidongrenwu": "先启动任务，待字幕翻译完成后可试听,配音速度、自动加速实时修改生效", "juanzhu": " 小额捐赠帮助待续更新优化 / 免责声明 ", "nocuda": "你的设备不满足CUDA加速要求，请确认是NVIDIA显卡，并已配置好CUDA环境，点击菜单栏-帮助支持-CUDA help", "nocudnn": "你的设备没有安装配置cuDNN，请参考 https://juejin.cn/post/7318704408727519270#heading-1 安装，然后重启软件，或者改用openai模型", "cudatips": "如果有英伟达显卡并且配置好了CUDA环境，可启用，将极大提升执行速度", "noselectrole": "未选择角色，不可试听", "chongtingzhong": "重听中", "shitingzhong": "试听中/点击重听", "bukeshiting": "无字幕内容，不可试听", "tiquzimu": "原始语言设为视频发音语言，目标语言设为想翻译为的语言", "kaishitiquhefanyi": "开始识别和翻译", "tiquzimuno": "原始语言设为视频发音语言", "kaishitiquzimu": "开始识别字幕", "zimu_video": "选择要合并的视频，将字幕srt文件拖拽到右侧字幕区", "zimu_peiyin": "请将目标语言设为字幕所用语言，并选择配音角色", "kaishipeiyin": "开始配音", "anzhuangvlc": "你可能需要先安装VLC解码器，", "jixuzhong": "继续执行中", "nextstep": "继续下一步", "tianxieazure": "必须在菜单-设置中填写Azure key", "bencijieshu": "本次任务结束", "kaishichuli": "开始处理", "yunxingbukerole": "运行中，不可改为无配音角色", "bixutianxie": "必须填写", "peiyinmoshisrt": "配音模式下必须选择配音角色、目标语言、并将本地srt字幕文件拖拽到右侧字幕区", "hebingmoshisrt": "合并模式下，必须选择视频、字幕嵌入类型、并将字幕srt文件拖拽到右侧字幕区", "fanyimoshi1": "必须选择要翻译到的目标语言", "bukedoubucunzai": "视频和字幕不能同时都不存在哦！", "wufapeiyin": "没有选择目标语言，无法进行配音哦，请选择目标语言或取消配音角色", "xuanzeyinpinwenjian": "选择音视频文件", "vlctips": "拖动视频到此或者双击选择视频", "vlctips2": " 文件不存在", "xuanzeyinshipin": "点击选择或拖拽一个或多个音频视频文件到此处", "tuodongdaoci": "拖动要转换的文件到此处松开", "tuodongfanyi": "请导入要翻译的srt字幕文件", "zhixingwc": "执行完成/点击开始", "zhixinger": "执行出错", "starttrans": "开始翻译", "yinpinhezimu": "音频和字幕至少要选择一个", "bixuyinshipin": "必须选择有效的音视频文件", "chakanerror": "识别前预处理失败，请确认视频中是否含有音频数据", "srtisempty": "字幕内容为空", "savesrtto": "选择保存字幕文件到..", "neirongweikong": "内容不能为空", "yuyanjuesebixuan": "语言和角色必须选择", "nojueselist": "未获取到角色列表", "buzhichijuese": "不支持该语音角色", "nowenjian": "不存在有效文件", "yinpinbuke": "音频不可转为", "quanbuend": "全部转换完成", "wenbenbukeweikong": "待翻译文本不可为空", "buzhichifanyi": "不支持翻译到该目标语言", "ffmpegno": "未找到 ffmpeg，软件不可用，请去 ffmpeg.org 下载并加入到系统环境变量", "newversion": "有新的版本了，点击去下载", "tingzhile": "停止了", "geshihuazimuchucuo": "格式化字幕文件出错", "moweiyanchangshibai": "末尾添加延长视频帧失败，将保持原样不延长视频", "xiugaiyuanyuyan": " 即将翻译，可以点击暂停后修改字幕/点此立即翻译 ", "jimiaohoufanyi": "秒后自动翻译，可以暂停后去修改字幕，以便翻译更准确", "xiugaipeiyinzimu": " 即将配音,可以暂停后修改字幕/点击立即配音 ", "zidonghebingmiaohou": "秒后自动配音，你可以暂停后去修改字幕，以便配音更准确", "jieshutips": "视频处理结束:相关素材可在目标文件夹内查看，含字幕文件、配音文件等", "mubiao": "打开输出文件夹", "endandopen": "已完成，点击打开 ", "waitforstart": "等待开始", "xianxuanjuese": "请先选择TTS类型和角色", "shezhijueseline": "填写使用该角色配音的行数", "youzimuyouset": "字幕区显示完整字幕后才可按行设置多角色", "shezhijuese": "设置角色", "zhishaoxuanzeyihang": "至少要选中一行", "default": "默认", "sjselectmp4": "双击选择视频或拖拽视频到此处", "ffmpegerror": "请检查CUDA配置是否正确，或检查视频是否H264编码的mp4文件", "tuodonghuoshuru": "输入文字或者拖动字幕srt文件到此处", "kaishihebing": "开始合并与输出结果文件", "kaishishibie": "开始进行语音识别", "kaishitiquyinpin": "开始提取音频", "kaishiyuchuli": "开始对视频进行预处理为标准格式...", "fengeyinpinshuju": "语音识别前先分割数据进行", "yuyinshibiejindu": "语音识别进度", "yuyinshibiewancheng": "语音识别完成", "shipinmoweiyanchang": "视频末尾延长", "shipinjiangsu": "视频延长", "xinshipinchangdu": "降速后新视频时长", "peiyin-yingzimu": "正在合成配音+硬字幕", "peiyin-ruanzimu": "正在合成配音+软字幕", "onlypeiyin": "正在嵌入配音，无字幕", "onlyyingzimu": "正在嵌入硬字幕，无配音", "onlyruanzimu": "正在嵌入软字幕，无配音", "zimuhangshu": "当前字幕行数 ", "huituicpu": "GPU上执行出错，回退到CPU执行", "zimuwenjianbuzhengque": "字幕文件不正确，无有效字幕", "mansuchucuo": "视频自动慢速出错，请尝试取消‘视频自动慢速’选项", "qianyiwenjian": "路径或名称含有空格或特殊符号，为避免后续处理出错，将移动到"}, "ui_lang": {"action_xinshoujandan": "简单新手模式", "action_xinshoujandan_tools": "无需配置使用简单，适合短视频，准确度较低，更多控制请使用标准功能模式", "onlyvideo": "仅保存视频", "onlyvideo_tips": "如果选中该项，将不保留字幕、音频等素材，仅保存最终生成的视频文件", "model_type_tips": "faster模型更快更节省资源，但若需CUDA加速除cuda外还需要再安装配置cudnn和cublas\nopenai模型执行较慢也更耗资源，但CUDA加速仅需安装cuda\nGoogleSpeech是使用google识别，无需安装模型但需设置代理，效果略差", "addbackbtn": "添加背景声音", "back_audio_place": "背景音乐的完整路径，删除则不添加", "Download Models": "下载识别模型", "Download cuBLASxx.dll": "下载cuBLASxx.dll", "Separate vocal voice": "背景人声分离", "SP-video Translate Dubbing": "VideoTrans视频翻译配音", "Multiple MP4 videos can be selected and automatically queued for processing": "可选择多个mp4视频，自动排队处理", "Select video..": "选择视频..", "Select where to save the processed output resources": "选择处理后输出的资源保存到哪里", "Save to..": "保存到..", "Open target dir": "打开保存目录", "Open": "打开", "Translate channel": "翻译渠道", "Proxy": "网络代理地址", "proxy address": "Google/chatGPT官方接口国内无法访问，必须填写网络代理地址,不是填写api地址", "shuoming01": "点击试听当前配音角色的发音\n生成配音可能需要数秒，请耐心等待", "Trial dubbing": "试听配音", "Source lang": "原始语言", "The language used for the original video pronunciation": "原视频发音所用语言", "Target lang": "目标语言", "What language do you want to translate into": "你希望翻译为哪种语言", "Dubbing role": "配音角色", "No is not dubbing": "选No代表不进行配音", "From base to large v3, the effect is getting better and better, but the speed is also getting slower and slower": "从 tiny 到 large-v3，效果越来越好，但也需要更多计算机资源\nCUDA加速时，12G以下GPU不建议使用large系模型，可能闪退\nen后缀模型只用于识别英文视频", "Overall recognition is suitable for videos with or without background music and noticeable silence": "整体识别适合有无背景音乐，有明显静音的视频。", "Embed subtitles": "嵌入字幕", "shuoming02": "硬字幕无论在哪里播放始终显示字幕，不可隐藏。\n软字幕如果播放器支持，可在播放器中控制显示或隐藏。\n如果你想网页中播放时显示字幕，请选择硬字幕。\n硬字幕可在 videotrans/set.ini 中修改fontsize控制大小", "Silent duration": "静音片段", "default 500ms": "默认500ms，越小切分的片段越多", "Mute duration for segmented speech, in milliseconds": "分割语音的静音时长，单位ms", "Dubbing speed": "配音整体语速", "Overall acceleration or deceleration of voice over playback": "对配音语速整体加速或降速播放，正数加速，负数减速", "Positive numbers accelerate, negative numbers decelerate, -90 to+90": "正数则加速，负数则减速，-90到+90", "shuoming03": "翻译后不同语言下发音时长不同，必然会出现对齐问题，通过配音整体语速、配音自动加速、语音前后延展可稍缓解，更多方法和原理请查看左下角相关教程", "Voice acceleration?": "配音自动加速", "Video slow": "视频自动慢速", "shuoming05": "必须确定有NVIDIA显卡且正确配置了CUDA环境，否则勿选", "Enable CUDA?": "CUDA加速", "Preserve background music": "保留背景音", "If retained, the required time may be longer, please be patient and wait": "若保留，所需时间可能较长，请耐心等待\n使用clone-voice时，为保证效果请选中", "Start": "开始", "Pause": "点击暂停", "Import srt": "导入字幕", "Train voice": "试听", "Set role by line": "设置多角色", "&Setting": "设置TTS与翻译Key(&S)", "&Tools": "工具(&T)", "&Help": "帮助/关于(&H)", "toolBar": "toolBar", "Video Toolbox": "视频工具箱", "Go VLC Website": "去VLC官网", "FFmpeg": "FFmpeg", "Go FFmpeg website": "去FFmpeg官网", "Post issue": "遇到问题?", "Clone Voice": "音色克隆", "Documents": "文档/官网", "Donating developers": "小额捐赠/开发者信息", "Standard Function Mode": "标准功能模式", "Display all options for video translation and dubbing": "显示全部选项，高度自定义进行视频翻译和配音", "Export  Srt  From Videos": "视频识别字幕", "Extracting SRT subtitles in the original language from local videos": "从本地视频的语音中识别出文字并生成srt字幕", "Merging Subtitle  Video": "字幕嵌入视频", "Embed locally existing SRT subtitles into the video": "将已有的srt字幕嵌入视频中", "Subtitle Create Dubbing": "单个字幕配音", "Local existing SRT subtitle generation dubbing WAV files": "根据srt字幕文件创建配音文件", "Speech Recognition Text": "音视频转字幕", "Recognize the sound in audio or video and output SRT text": "将音频或视频中的声音识别后输出srt字幕文件", "From  Text  Into  Speech": "批量字幕配音", "Generate audio WAV from text or SRT subtitle files": "根据srt字幕文件创建配音文件", "Extract Srt And Translate": "视频转为字幕", "Extract SRT subtitles from local videos in the original language and translate them into SRT subtitle files in the target language": "识别视频中的语音为srt字幕", "Separate Video to audio": "从视频取音频", "Separate audio and silent videos from videos": "将视频分离为音频文件和无声mp4视频文件", "Video Subtitles Merging": "声画字幕合并", "Merge audio, video, and subtitles into one file": "将音频、视频、字幕合并为一个文件", "Files Format Conversion": "文件格式转换", "Convert various formats to each other": "各种格式互相转换", "Mixing 2 Audio Streams": "两个音频混流", "Mix two audio files into one audio file": "将两个音频文件混流为一个音频文件", "Text  Or Srt  Translation": "批量翻译字幕", "Translate text or subtitles": "将多个字幕批量进行翻译", "Download from Youtube": "下载油管视频", "Whisper model": "语音模型", "faster model": "faster模式", "openai model": "openai模式"}, "toolbox_lang": {"import audio or video": "导入音视频文件", "Video Toolbox": "视频工具箱", "Start": "开始执行", "No voice video": "无声视频", "Open dir": "打开目录", "Audio Wav": "音频文件", "Video audio separation": "从视频中分离提取音频", "Video file": "视频文件", "Select video": "选择视频", "Audio file": "音频文件", "Select audio": "选择音频", "Subtitle srt": "字幕srt", "Select srt file": "选择字幕", "Open output dir": "打开输出目录", "Video subtitle merging": "声画字幕合并", "Source lang": "发音语言", "Whisper model": "语音识别模型", "Save to srt..": "保存为srt文件..", "Voice recognition": "音视频转字幕", "Subtitle lang": "字幕语言", "Select role": "选择角色", "Speed change": "速度变化百分比", "Negative deceleration, positive acceleration": "负数降速，正数加速", "If so, the line number and time value will skip reading aloud": "如果是，则行号和时间值将跳过朗读", "Is srt?": "是srt字幕?", "Automatic acceleration?": "自动加速?", "Output audio name": "输出音频名称", "Set the name of the generated audio file here. If not filled in, use the time and date command": "在这里设置生成的音频文件名称，不填写则使用时间日期命令", "Text to speech": "批量字幕创建配音", "Convert mp4->": "转为mp4->", "Convert avi->": "转为avi->", "Convert mov->": "转为mov->", "Convert wav->": "转为wav->", "Convert mp3->": "转为mp3->", "Convert aac->": "转为aac->", "Convert m4a->": "转为m4a->", "Conver flac->": "转为flac->", "The conversion result is displayed here": "这里显示转换结果", "Audio and video format conversion": "音视频格式转换", "Audio file 1": "音频文件1", "Select the first audio file": "选择第一个音频文件", "Audio file 2": "音频文件2", "Select the second audio file": "选择第二个音频文件", "You can customize the output file name here. If not filled in, use a date name": "你可在此自定义输出文件名字，不填写则使用日期命名", "Mixing two audio streams": "两个音频混流", "Translation channels": "翻译渠道", "Target lang": "目标语言", "Proxy": "网络代理地址", "Failed to access Google services. Please set up the proxy correctly": "填写网络代理(vpn)地址,Google/ChaGPT国内无法访问，必须填写", "Import text to be translated from a file..": "从srt文件导入字幕/1或多个", "shuoming1": "只允许翻译srt格式的字幕文件，不符合该格式的请勿导入翻译，否则将报错", "export..": "导出结果..", "Start>": "立即翻译>", "The translation result is displayed here": "这里显示翻译结果", "Text subtitle translation": "批量翻译字幕"}, "language_code_list": {"zh": "中文", "en": "英语", "fr": "法语", "de": "德语", "ja": "日语", "ko": "韩语", "ru": "俄语", "es": "西班牙语", "th": "泰国语", "it": "意大利语", "pt": "葡萄牙语", "vi": "越南语"}, "model_code_list": {"云模型": {"status": 1, "model_name": "small.en"}, "中文模型": {"status": 302, "model_name": "speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch"}, "多语言模型": {"status": 303, "model_name": "SenseVoiceSmall"}}}